import { createStorage } from 'unstorage'
import redisDriver from 'unstorage/drivers/redis'

export default defineEventHandler(async (event) => {
	// flush the redis database using unstorage driver

	try {
		const redis = createStorage({
			driver: redisDriver({
				tls: {
					host: 'v2-8e6rqo.serverless.euw1.cache.amazonaws.com',
					port: 6379,
				},
			}),
		})

		await redis.clear('', {
			
		})

		return {
			message: 'Cache cleared',
		}
	} catch (error) {
		return {
			message: 'Failed to clear cache',
			error,
		}
	}
})
